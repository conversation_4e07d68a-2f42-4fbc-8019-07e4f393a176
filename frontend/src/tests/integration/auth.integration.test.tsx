/**
 * Authentication Integration Tests
 * Tests the complete authentication flow with API integration
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { http, HttpResponse } from 'msw';
import { server } from '../../testing/mocks/server';

// Import components to test
import Login from '../../pages/Login';
import Register from '../../pages/Register';

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Authentication Integration Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    // Clear any stored tokens
    localStorage.clear();
    sessionStorage.clear();
  });

  describe('Login Flow', () => {
    it('should successfully log in a user with valid credentials', async () => {
      render(
        <TestWrapper>
          <Login />
        </TestWrapper>
      );

      // Fill in the login form
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /log in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      // Wait for successful login
      await waitFor(() => {
        expect(screen.queryByText(/logging in/i)).not.toBeInTheDocument();
      });

      // Check that tokens are stored (if your app does this)
      // This depends on your authentication implementation
      // expect(localStorage.getItem('access_token')).toBeTruthy();
    });

    it('should show error message for invalid credentials', async () => {
      // Override the login handler to return an error
      server.use(
        http.post('/api/v1/auth/login', () => {
          return HttpResponse.json(
            { error: 'Invalid credentials' },
            { status: 401 }
          );
        })
      );

      render(
        <TestWrapper>
          <Login />
        </TestWrapper>
      );

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /log in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(loginButton);

      // Wait for error message
      await waitFor(() => {
        expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
      });
    });

    it('should handle network errors gracefully', async () => {
      // Override the login handler to simulate network error
      server.use(
        http.post('/api/v1/auth/login', () => {
          return HttpResponse.error();
        })
      );

      render(
        <TestWrapper>
          <Login />
        </TestWrapper>
      );

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /log in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      // Wait for network error message
      await waitFor(() => {
        expect(screen.getByText(/network error|something went wrong/i)).toBeInTheDocument();
      });
    });
  });

  describe('Registration Flow', () => {
    it('should successfully register a new user', async () => {
      render(
        <TestWrapper>
          <Register />
        </TestWrapper>
      );

      // Fill in the registration form
      const nameInput = screen.getByLabelText(/name/i);
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const registerButton = screen.getByRole('button', { name: /register|sign up/i });

      await user.type(nameInput, 'Test User');
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(registerButton);

      // Wait for successful registration
      await waitFor(() => {
        expect(screen.queryByText(/registering/i)).not.toBeInTheDocument();
      });

      // Check for success message or redirect
      await waitFor(() => {
        expect(
          screen.getByText(/registration successful|welcome/i) ||
          screen.getByText(/check your email/i)
        ).toBeInTheDocument();
      });
    });

    it('should show validation errors for invalid input', async () => {
      render(
        <TestWrapper>
          <Register />
        </TestWrapper>
      );

      const registerButton = screen.getByRole('button', { name: /register|sign up/i });

      // Try to submit empty form
      await user.click(registerButton);

      // Wait for validation errors
      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      });
    });

    it('should handle registration errors from server', async () => {
      // Override the registration handler to return an error
      server.use(
        http.post('/api/v1/auth/register', () => {
          return HttpResponse.json(
            { error: 'Email already exists' },
            { status: 409 }
          );
        })
      );

      render(
        <TestWrapper>
          <Register />
        </TestWrapper>
      );

      const nameInput = screen.getByLabelText(/name/i);
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const registerButton = screen.getByRole('button', { name: /register|sign up/i });

      await user.type(nameInput, 'Test User');
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(registerButton);

      // Wait for error message
      await waitFor(() => {
        expect(screen.getByText(/email already exists/i)).toBeInTheDocument();
      });
    });
  });

  describe('Token Refresh Flow', () => {
    it('should automatically refresh expired tokens', async () => {
      // This test would depend on your token refresh implementation
      // Mock an expired token scenario and verify refresh behavior
      
      // Set up initial expired token
      localStorage.setItem('access_token', 'expired-token');
      localStorage.setItem('refresh_token', 'valid-refresh-token');

      // Override handlers to simulate token refresh
      server.use(
        http.get('/api/v1/users/me', () => {
          return HttpResponse.json(
            { error: 'Token expired' },
            { status: 401 }
          );
        }),
        http.post('/api/v1/auth/refresh', () => {
          return HttpResponse.json({
            access_token: 'new-access-token',
          });
        })
      );

      // This would test your automatic token refresh logic
      // Implementation depends on your auth system
    });
  });
});
