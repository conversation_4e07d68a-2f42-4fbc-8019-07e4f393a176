import React, { useRef, useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useMediaQuery } from '../../hooks/useMediaQuery';

interface SignatureCanvasProps {
  onSignatureCapture: (signatureDataUrl: string) => void;
  width?: number;
  height?: number;
  clearOnSubmit?: boolean;
  initialPenColor?: string;
  initialPenThickness?: number;
  className?: string;
  compact?: boolean; // For smaller UI in constrained spaces
  onCancel?: () => void; // Optional cancel handler
}

type PenColor = {
  color: string;
  label: string;
};

const SignatureCanvas: React.FC<SignatureCanvasProps> = ({
  onSignatureCapture,
  width: propWidth,
  height: propHeight,
  clearOnSubmit = true,
  initialPenColor = '#000000',
  initialPenThickness = 2,
  className = '',
  compact = false,
  onCancel
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [hasSignature, setHasSignature] = useState(false);
  const [ctx, setCtx] = useState<CanvasRenderingContext2D | null>(null);
  const [penColor, setPenColor] = useState<string>(initialPenColor);
  const [penThickness, setPenThickness] = useState<number>(initialPenThickness);
  const [canvasWidth, setCanvasWidth] = useState<number>(propWidth || 500);
  const [canvasHeight, setCanvasHeight] = useState<number>(propHeight || 200);
  const [showColorPicker, setShowColorPicker] = useState<boolean>(false);
  const [showThicknessPicker, setShowThicknessPicker] = useState<boolean>(false);

  // Use media query to determine if we're on mobile
  const isMobile = useMediaQuery('(max-width: 640px)');
  const isTablet = useMediaQuery('(min-width: 641px) and (max-width: 1024px)');

  // Available pen colors
  const penColors: PenColor[] = [
    { color: '#000000', label: 'Black' },
    { color: '#0000FF', label: 'Blue' },
    { color: '#FF0000', label: 'Red' },
    { color: '#008000', label: 'Green' },
    { color: '#800080', label: 'Purple' },
    { color: '#FFA500', label: 'Orange' }
  ];

  // Available pen thicknesses
  const penThicknesses = [1, 2, 3, 5, 7];

  // Resize canvas to fit container
  const resizeCanvas = useCallback(() => {
    if (containerRef.current && canvasRef.current) {
      const containerWidth = containerRef.current.clientWidth;
      const newWidth = propWidth || containerWidth;
      const newHeight = propHeight || (containerWidth * 0.4); // Maintain aspect ratio

      setCanvasWidth(newWidth);
      setCanvasHeight(newHeight);

      // If we already have a context, we need to redraw the signature
      if (ctx && hasSignature) {
        const oldCanvas = canvasRef.current;
        const oldImageData = ctx.getImageData(0, 0, oldCanvas.width, oldCanvas.height);

        // Update canvas size
        oldCanvas.width = newWidth;
        oldCanvas.height = newHeight;

        // Restore context properties
        ctx.lineWidth = penThickness;
        ctx.lineCap = 'round';
        ctx.strokeStyle = penColor;

        // Redraw the signature (scaled to fit new dimensions)
        ctx.putImageData(oldImageData, 0, 0);
      }
    }
  }, [propWidth, propHeight, ctx, hasSignature, penColor, penThickness]);

  // Initialize canvas context and set up resize listener
  useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      const context = canvas.getContext('2d');
      if (context) {
        context.lineWidth = penThickness;
        context.lineCap = 'round';
        context.strokeStyle = penColor;
        setCtx(context);
      }
    }

    // Initial resize
    resizeCanvas();

    // Add resize event listener
    window.addEventListener('resize', resizeCanvas);

    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
    };
  }, []);

  // Update pen settings when they change
  useEffect(() => {
    if (ctx) {
      ctx.lineWidth = penThickness;
      ctx.strokeStyle = penColor;
    }
  }, [penColor, penThickness, ctx]);

  // Start drawing
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!ctx) return;

    setIsDrawing(true);
    setHasSignature(true);

    // Get canvas position
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();

    // Handle both mouse and touch events
    let clientX, clientY;

    if ('touches' in e) {
      // Touch event
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
    } else {
      // Mouse event
      clientX = e.clientX;
      clientY = e.clientY;
    }

    // Calculate position relative to canvas
    const x = clientX - rect.left;
    const y = clientY - rect.top;

    ctx.beginPath();
    ctx.moveTo(x, y);
  };

  // Draw
  const draw = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !ctx) return;

    // Get canvas position
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();

    // Handle both mouse and touch events
    let clientX, clientY;

    if ('touches' in e) {
      // Touch event
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;

      // Prevent scrolling while drawing
      e.preventDefault();
    } else {
      // Mouse event
      clientX = e.clientX;
      clientY = e.clientY;
    }

    // Calculate position relative to canvas
    const x = clientX - rect.left;
    const y = clientY - rect.top;

    ctx.lineTo(x, y);
    ctx.stroke();
  };

  // Stop drawing
  const stopDrawing = () => {
    if (isDrawing && ctx) {
      ctx.closePath();
      setIsDrawing(false);
    }
  };

  // Clear canvas
  const clearCanvas = () => {
    if (ctx && canvasRef.current) {
      ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
      setHasSignature(false);
    }
  };

  // Capture signature
  const captureSignature = () => {
    if (canvasRef.current && hasSignature) {
      const dataUrl = canvasRef.current.toDataURL('image/png');
      onSignatureCapture(dataUrl);

      if (clearOnSubmit) {
        clearCanvas();
      }
    }
  };

  // Handle pen color change
  const handleColorChange = (color: string) => {
    setPenColor(color);
    if (ctx) {
      ctx.strokeStyle = color;
    }
    setShowColorPicker(false);
  };

  // Handle pen thickness change
  const handleThicknessChange = (thickness: number) => {
    setPenThickness(thickness);
    if (ctx) {
      ctx.lineWidth = thickness;
    }
    setShowThicknessPicker(false);
  };

  // Toggle color picker
  const toggleColorPicker = () => {
    setShowColorPicker(!showColorPicker);
    setShowThicknessPicker(false);
  };

  // Toggle thickness picker
  const toggleThicknessPicker = () => {
    setShowThicknessPicker(!showThicknessPicker);
    setShowColorPicker(false);
  };

  return (
    <div className={`signature-canvas-container ${className}`}>
      {/* Pen Settings - Mobile Friendly */}
      <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'justify-between items-center'} mb-3`}>
        <div className="flex items-center space-x-2">
          <span className={`${compact ? 'text-xs' : 'text-sm'} text-gray-600`}>Pen:</span>

          {/* Color Selector Button */}
          <div className="relative">
            <button
              type="button"
              onClick={toggleColorPicker}
              className="flex items-center space-x-1 px-2 py-1 bg-white border border-gray-300 rounded-md touch-friendly"
              aria-expanded={showColorPicker}
              aria-haspopup="true"
            >
              <div
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: penColor }}
                aria-hidden="true"
              />
              <span className={`${compact ? 'text-xs' : 'text-sm'}`}>Color</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>

            {/* Color Dropdown */}
            <AnimatePresence>
              {showColorPicker && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute z-10 mt-1 bg-white border border-gray-200 rounded-md shadow-lg p-2"
                >
                  <div className="grid grid-cols-3 gap-1">
                    {penColors.map((colorOption) => (
                      <button
                        key={colorOption.color}
                        type="button"
                        title={colorOption.label}
                        onClick={() => handleColorChange(colorOption.color)}
                        className={`w-8 h-8 rounded-full border touch-friendly ${
                          penColor === colorOption.color ? 'ring-2 ring-primary ring-offset-1' : 'border-gray-300'
                        }`}
                        style={{ backgroundColor: colorOption.color }}
                        aria-label={`Select ${colorOption.label} pen color`}
                      />
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Thickness Selector Button */}
          <div className="relative">
            <button
              type="button"
              onClick={toggleThicknessPicker}
              className="flex items-center space-x-1 px-2 py-1 bg-white border border-gray-300 rounded-md touch-friendly"
              aria-expanded={showThicknessPicker}
              aria-haspopup="true"
            >
              <div className="flex items-center">
                <div
                  className="rounded-full bg-black"
                  style={{
                    width: `${Math.min(penThickness * 2, 10)}px`,
                    height: `${Math.min(penThickness * 2, 10)}px`
                  }}
                  aria-hidden="true"
                />
              </div>
              <span className={`${compact ? 'text-xs' : 'text-sm'}`}>Size</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>

            {/* Thickness Dropdown */}
            <AnimatePresence>
              {showThicknessPicker && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute z-10 mt-1 bg-white border border-gray-200 rounded-md shadow-lg p-2"
                >
                  <div className="flex space-x-1">
                    {penThicknesses.map((thickness) => (
                      <button
                        key={thickness}
                        type="button"
                        onClick={() => handleThicknessChange(thickness)}
                        className={`flex items-center justify-center w-8 h-8 rounded border touch-friendly ${
                          penThickness === thickness ? 'bg-primary text-white' : 'bg-white text-gray-700 border-gray-300'
                        }`}
                        aria-label={`Set pen thickness to ${thickness}px`}
                      >
                        {thickness}
                      </button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Clear Button */}
        <button
          type="button"
          onClick={clearCanvas}
          className={`
            ${compact ? 'px-3 py-1 text-xs' : 'px-4 py-1.5 text-sm'}
            text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors touch-friendly
          `}
        >
          <span className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
            Clear
          </span>
        </button>
      </div>

      {/* Canvas */}
      <div
        ref={containerRef}
        className="border border-gray-300 rounded-md mb-3 bg-white relative overflow-hidden"
      >
        <canvas
          ref={canvasRef}
          width={canvasWidth}
          height={canvasHeight}
          className="cursor-crosshair touch-none w-full h-full"
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseLeave={stopDrawing}
          onTouchStart={startDrawing}
          onTouchMove={draw}
          onTouchEnd={stopDrawing}
        />
        {!hasSignature && (
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <p className={`${compact ? 'text-xs' : 'text-sm'} text-gray-400`}>Sign here</p>
          </div>
        )}
      </div>

      {/* Controls */}
      <div className={`${onCancel ? 'flex justify-between' : 'flex justify-end'}`}>
        {onCancel && (
          <button
            type="button"
            onClick={onCancel}
            className={`
              ${compact ? 'px-3 py-1.5 text-xs' : 'px-4 py-2 text-sm'}
              text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors touch-friendly
            `}
          >
            Cancel
          </button>
        )}

        <motion.button
          whileTap={{ scale: 0.95 }}
          type="button"
          onClick={captureSignature}
          disabled={!hasSignature}
          className={`
            ${compact ? 'px-3 py-1.5 text-xs' : 'px-4 py-2 text-sm'}
            rounded-md transition-colors touch-friendly
            ${hasSignature
              ? 'bg-primary text-white hover:bg-primary-dark'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'}
          `}
        >
          <span className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            Confirm Signature
          </span>
        </motion.button>
      </div>

      <p className={`${compact ? 'text-xs' : 'text-sm'} text-gray-500 mt-2 text-center`}>
        Please sign above using your {isMobile || isTablet ? 'finger' : 'mouse or touch screen'}
      </p>
    </div>
  );
};

export default SignatureCanvas;