"""
RentUp Backend Main Application

This is the main FastAPI application entry point for the RentUp backend.
"""

import logging
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from contextlib import asynccontextmanager

# Import configuration
from app.core.config import settings

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("🚀 Starting RentUp Backend API...")
    logger.info(f"📊 Environment: {getattr(settings, 'ENVIRONMENT', 'development')}")
    logger.info(f"🔗 API Version: {settings.API_V1_STR}")
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down RentUp Backend API...")


# Create FastAPI application
app = FastAPI(
    title="RentUp Backend API",
    description="Backend API for the RentUp rental marketplace platform",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Health check endpoint
@app.get("/health")
@app.get("/api/v1/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "message": "RentUp Backend API is running",
        "version": "1.0.0",
        "api_version": settings.API_V1_STR
    }


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Welcome to RentUp Backend API",
        "docs": "/docs",
        "health": "/health",
        "api_version": settings.API_V1_STR
    }


# API v1 endpoints
@app.get("/api/v1/")
async def api_v1_root():
    """API v1 root endpoint."""
    return {
        "message": "RentUp API v1",
        "endpoints": {
            "health": "/api/v1/health",
            "users": "/api/v1/users",
            "items": "/api/v1/items",
            "rentals": "/api/v1/rentals",
            "auctions": "/api/v1/auctions",
            "agreements": "/api/v1/agreements"
        }
    }


# Placeholder endpoints for testing
@app.get("/api/v1/users")
async def get_users():
    """Get users endpoint."""
    return {
        "message": "Users endpoint",
        "users": [],
        "total": 0
    }


@app.get("/api/v1/items")
async def get_items():
    """Get items endpoint."""
    return {
        "message": "Items endpoint",
        "items": [],
        "total": 0
    }


@app.get("/api/v1/rentals")
async def get_rentals():
    """Get rentals endpoint."""
    return {
        "message": "Rentals endpoint",
        "rentals": [],
        "total": 0
    }


@app.get("/api/v1/auctions")
async def get_auctions():
    """Get auctions endpoint."""
    return {
        "message": "Auctions endpoint",
        "auctions": [],
        "total": 0
    }


@app.get("/api/v1/agreements")
async def get_agreements():
    """Get agreements endpoint."""
    return {
        "message": "Agreements endpoint",
        "agreements": [],
        "total": 0
    }


# Database optimization endpoints
@app.get("/api/v1/optimization/stats")
async def get_optimization_stats():
    """Get database optimization statistics."""
    try:
        from app.services.db_optimization_service import get_optimization_service
        service = get_optimization_service()
        stats = service.get_performance_stats()
        return {
            "status": "success",
            "stats": stats
        }
    except Exception as e:
        logger.error(f"Failed to get optimization stats: {e}")
        return {
            "status": "error",
            "message": "Optimization modules not available",
            "error": str(e)
        }


# Error handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    """Handle 404 errors."""
    return JSONResponse(
        status_code=404,
        content={
            "status": "error",
            "message": "Endpoint not found",
            "path": str(request.url.path)
        }
    )


@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    """Handle 500 errors."""
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "status": "error",
            "message": "Internal server error",
            "path": str(request.url.path)
        }
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
