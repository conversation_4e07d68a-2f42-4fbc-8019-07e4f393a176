# FastAPI and dependencies
fastapi==0.116.0  # Updated from 0.115.12 (May 2025)
uvicorn==0.35.0  # Updated from 0.34.2 (May 2025)
pydantic==2.12.0  # Updated from 2.11.4 (May 2025)
pydantic-settings==2.10.0  # Updated from 2.9.1 (May 2025)
email-validator==2.3.0  # Updated from 2.2.0 (May 2025)

# Database
sqlalchemy==2.0.41  # Updated from 2.0.40 (May 14, 2025)
alembic==1.15.3  # Updated from 1.15.2 (May 2025)
psycopg2-binary==2.9.11  # Updated from 2.9.10 (May 2025)

# Authentication
python-jose[cryptography]==3.4.0
argon2-cffi==23.1.0     # Modern password hashing (winner of Password Hashing Competition)
passlib==1.7.4          # For legacy password hash support
python-multipart==0.0.21  # Updated from 0.0.20 (May 2025)
fido2==1.1.3  # Updated from 1.1.2 (May 2025)
qrcode==7.5.0  # Updated from 7.4.2 (May 2025)

# Redis for token blacklisting
redis==6.0.1  # Updated from 6.0.0 (May 2025)
hiredis==3.2.0  # Updated from 3.1.1 (May 2025)

# Testing
pytest==8.4.0  # Updated from 8.3.5 (May 2025)
pytest-mock==3.15.0  # Updated from 3.14.0 (May 2025)
httpx==0.29.0  # Updated from 0.28.1 (May 2025)
tenacity==9.2.0  # Updated from 9.1.2 (May 2025)

# Utilities
python-dotenv==1.1.1  # Updated from 1.1.0 (May 2025)
loguru==0.8.0  # Updated from 0.7.3 (May 2025)
aiofiles==24.2.0  # Updated from 24.1.0 (May 2025)
orjson==3.11.0  # Updated from 3.10.18 (May 2025)
jinja2==3.1.5  # Updated from 3.1.4 (May 2025)
pdfkit==1.0.0
weasyprint==62.0  # Updated from 61.2 (May 2025)
pillow==10.4.0  # Updated from 10.3.0 (May 2025)
brotli==1.1.0  # For Brotli compression

# AI/ML
torch==2.8.0  # Updated from 2.7.0 (May 2025)
transformers==4.52.0  # Updated from 4.51.3 (May 2025)
sentence-transformers==4.2.0  # Updated from 4.1.0 (May 2025)
qdrant-client==1.15.0  # Updated from 1.14.2 (May 2025)

# AWS
boto3==1.39.0  # Updated from 1.38.13 (May 2025)

# Payment processing
stripe==8.6.0  # Updated from 8.5.0 (May 2025)

# Performance monitoring and profiling
py-spy==0.4.0  # For profiling Python applications
yappi==1.5.0  # For profiling async code
prometheus-client==0.19.0  # For metrics collection
locust==2.24.0  # For load testing
matplotlib==3.9.0  # For visualization of performance metrics
cachetools==5.4.0  # For in-memory caching
