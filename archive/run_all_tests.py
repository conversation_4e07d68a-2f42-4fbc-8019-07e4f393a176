#!/usr/bin/env python3
"""
Comprehensive Test Runner for RentUp Platform

This script runs all tests for the RentUp platform, including:
1. Backend unit tests
2. Backend integration tests
3. Frontend tests
4. End-to-end tests

Usage:
    python run_all_tests.py [--backend-only] [--frontend-only] [--phase PHASE]

Options:
    --backend-only    Run only backend tests
    --frontend-only   Run only frontend tests
    --phase PHASE     Run tests for a specific phase (0, 1, 2, 3)
    --verbose         Show detailed test output
"""

import os
import sys
import subprocess
import argparse
import time
from datetime import datetime
from pathlib import Path

# Define colors for terminal output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

# Define test categories
BACKEND_UNIT_TESTS = [
    "backend/app/tests",
    "tests"
]

BACKEND_API_TESTS = [
    "backend/tests/api"
]

FRONTEND_TESTS = [
    "frontend/tests",
    "frontend/cypress"
]

# Define phase-specific tests
PHASE0_TESTS = [
    "testResults/phase0/test-phase0.sh"
]

PHASE1_TESTS = [
    "frontend/cypress/e2e/phase1"
]

PHASE2_TESTS = [
    "backend/tests/api/test_search.py",
    "backend/tests/api/test_categories.py",
    "backend/tests/api/test_uploads.py",
    "backend/tests/api/test_social_login.py"
]

PHASE3_TESTS = [
    "tests/test_analytics_service.py",
    "tests/test_user_behavior_service.py",
    "backend/app/tests/test_recommendations.py"
]

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run all tests for RentUp platform")
    parser.add_argument("--backend-only", action="store_true", help="Run only backend tests")
    parser.add_argument("--frontend-only", action="store_true", help="Run only frontend tests")
    parser.add_argument("--phase", type=int, choices=[0, 1, 2, 3], help="Run tests for a specific phase")
    parser.add_argument("--verbose", action="store_true", help="Show detailed test output")
    return parser.parse_args()

def print_header(message):
    """Print a formatted header message."""
    print(f"\n{Colors.HEADER}{Colors.BOLD}{'=' * 80}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{message.center(80)}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{'=' * 80}{Colors.ENDC}\n")

def print_subheader(message):
    """Print a formatted subheader message."""
    print(f"\n{Colors.OKBLUE}{Colors.BOLD}{message}{Colors.ENDC}")
    print(f"{Colors.OKBLUE}{'-' * len(message)}{Colors.ENDC}\n")

def run_command(command, cwd=None, verbose=False):
    """Run a shell command and return the result."""
    print(f"{Colors.OKBLUE}Running: {' '.join(command)}{Colors.ENDC}")
    
    try:
        if verbose:
            # Run with output displayed in real-time
            process = subprocess.Popen(
                command,
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1
            )
            
            # Print output in real-time
            for line in iter(process.stdout.readline, ''):
                print(line, end='')
            
            process.stdout.close()
            return_code = process.wait()
        else:
            # Run with output captured
            result = subprocess.run(
                command,
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True
            )
            return_code = result.returncode
        
        if return_code == 0:
            print(f"{Colors.OKGREEN}Command completed successfully{Colors.ENDC}")
            return True
        else:
            print(f"{Colors.FAIL}Command failed with return code {return_code}{Colors.ENDC}")
            if not verbose:
                print(f"{Colors.WARNING}Run with --verbose to see detailed output{Colors.ENDC}")
            return False
    
    except Exception as e:
        print(f"{Colors.FAIL}Error running command: {e}{Colors.ENDC}")
        return False

def run_pytest(test_path, verbose=False):
    """Run pytest on a specific path."""
    command = ["python", "-m", "pytest", test_path]
    if verbose:
        command.append("-v")
    
    # Determine the appropriate working directory
    if test_path.startswith("backend/"):
        cwd = "."
    else:
        cwd = "."
    
    return run_command(command, cwd=cwd, verbose=verbose)

def run_cypress(test_path, verbose=False):
    """Run Cypress tests."""
    command = ["npx", "cypress", "run"]
    if test_path:
        command.extend(["--spec", test_path])
    
    return run_command(command, cwd="frontend", verbose=verbose)

def run_shell_script(script_path, verbose=False):
    """Run a shell script."""
    command = ["bash", script_path]
    return run_command(command, verbose=verbose)

def run_backend_tests(verbose=False, phase=None):
    """Run all backend tests."""
    print_header("Running Backend Tests")
    
    test_paths = []
    
    # Add phase-specific tests if requested
    if phase is not None:
        if phase == 0:
            # Phase 0 doesn't have backend tests
            pass
        elif phase == 1:
            # Phase 1 doesn't have backend tests
            pass
        elif phase == 2:
            test_paths.extend(PHASE2_TESTS)
        elif phase == 3:
            test_paths.extend(PHASE3_TESTS)
    else:
        # Run all backend tests
        test_paths.extend(BACKEND_UNIT_TESTS)
        test_paths.extend(BACKEND_API_TESTS)
    
    # Run the tests
    results = {}
    for test_path in test_paths:
        print_subheader(f"Running tests in {test_path}")
        
        if test_path.endswith(".py"):
            success = run_pytest(test_path, verbose)
        elif test_path.endswith(".sh"):
            success = run_shell_script(test_path, verbose)
        else:
            success = run_pytest(test_path, verbose)
        
        results[test_path] = success
    
    # Print summary
    print_header("Backend Test Summary")
    all_passed = True
    for test_path, success in results.items():
        status = f"{Colors.OKGREEN}PASSED{Colors.ENDC}" if success else f"{Colors.FAIL}FAILED{Colors.ENDC}"
        print(f"{test_path}: {status}")
        if not success:
            all_passed = False
    
    return all_passed

def run_frontend_tests(verbose=False, phase=None):
    """Run all frontend tests."""
    print_header("Running Frontend Tests")
    
    test_paths = []
    
    # Add phase-specific tests if requested
    if phase is not None:
        if phase == 0:
            # Phase 0 has shell script tests
            test_paths.extend(PHASE0_TESTS)
        elif phase == 1:
            test_paths.extend(PHASE1_TESTS)
        elif phase == 2:
            # Use any Phase 2 frontend tests
            pass
        elif phase == 3:
            # Phase 3 doesn't have frontend tests yet
            pass
    else:
        # Run all frontend tests
        test_paths.extend(FRONTEND_TESTS)
    
    # Run the tests
    results = {}
    for test_path in test_paths:
        print_subheader(f"Running tests in {test_path}")
        
        if "cypress" in test_path:
            success = run_cypress(test_path, verbose)
        elif test_path.endswith(".sh"):
            success = run_shell_script(test_path, verbose)
        else:
            # Assume it's a Jest test
            command = ["npm", "test", "--", test_path]
            success = run_command(command, cwd="frontend", verbose=verbose)
        
        results[test_path] = success
    
    # Print summary
    print_header("Frontend Test Summary")
    all_passed = True
    for test_path, success in results.items():
        status = f"{Colors.OKGREEN}PASSED{Colors.ENDC}" if success else f"{Colors.FAIL}FAILED{Colors.ENDC}"
        print(f"{test_path}: {status}")
        if not success:
            all_passed = False
    
    return all_passed

def create_test_report(backend_success, frontend_success):
    """Create a test report file."""
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    report_dir = Path("testResults")
    report_dir.mkdir(exist_ok=True)
    
    report_path = report_dir / f"test_report_{timestamp}.md"
    
    with open(report_path, "w") as f:
        f.write(f"# RentUp Test Report\n\n")
        f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## Summary\n\n")
        backend_status = "PASSED" if backend_success else "FAILED"
        frontend_status = "PASSED" if frontend_success else "FAILED"
        overall_status = "PASSED" if backend_success and frontend_success else "FAILED"
        
        f.write(f"- Backend Tests: {backend_status}\n")
        f.write(f"- Frontend Tests: {frontend_status}\n")
        f.write(f"- Overall Status: {overall_status}\n\n")
        
        f.write("## Details\n\n")
        f.write("See console output for detailed test results.\n")
    
    print(f"\nTest report saved to {report_path}")
    return report_path

def main():
    """Main function."""
    args = parse_args()
    
    print_header("RentUp Comprehensive Test Runner")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    backend_success = True
    frontend_success = True
    
    # Run backend tests if requested
    if not args.frontend_only:
        backend_success = run_backend_tests(args.verbose, args.phase)
    
    # Run frontend tests if requested
    if not args.backend_only:
        frontend_success = run_frontend_tests(args.verbose, args.phase)
    
    # Create test report
    report_path = create_test_report(backend_success, frontend_success)
    
    # Print final summary
    print_header("Test Run Complete")
    print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if backend_success and frontend_success:
        print(f"\n{Colors.OKGREEN}{Colors.BOLD}All tests passed!{Colors.ENDC}")
        sys.exit(0)
    else:
        print(f"\n{Colors.FAIL}{Colors.BOLD}Some tests failed. See report for details.{Colors.ENDC}")
        sys.exit(1)

if __name__ == "__main__":
    main()
